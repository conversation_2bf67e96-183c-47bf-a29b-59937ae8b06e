"use client";

import React from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/app/lib/redux/store";

export default function ThemeShowcase() {
  const isDark = useSelector((state: RootState) => state.darkMode.value);

  return (
    <div className="space-y-8 max-w-4xl mx-auto">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-foreground mb-2">
          Theme Showcase
        </h2>
        <p className="text-muted-foreground">
          Currently in <span className="font-semibold">{isDark ? "Dark" : "Light"}</span> mode
        </p>
      </div>

      {/* Color Palette */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-background border border-border rounded-lg p-4 text-center">
          <div className="w-full h-12 bg-background border border-border rounded mb-2"></div>
          <p className="text-sm font-medium text-foreground">Background</p>
          <code className="text-xs text-muted-foreground">bg-background</code>
        </div>
        
        <div className="bg-card border border-border rounded-lg p-4 text-center">
          <div className="w-full h-12 bg-card border border-border rounded mb-2"></div>
          <p className="text-sm font-medium text-card-foreground">Card</p>
          <code className="text-xs text-muted-foreground">bg-card</code>
        </div>
        
        <div className="bg-muted border border-border rounded-lg p-4 text-center">
          <div className="w-full h-12 bg-muted border border-border rounded mb-2"></div>
          <p className="text-sm font-medium text-muted-foreground">Muted</p>
          <code className="text-xs text-muted-foreground">bg-muted</code>
        </div>
        
        <div className="bg-accent border border-border rounded-lg p-4 text-center">
          <div className="w-full h-12 bg-accent border border-border rounded mb-2"></div>
          <p className="text-sm font-medium text-accent-foreground">Accent</p>
          <code className="text-xs text-muted-foreground">bg-accent</code>
        </div>
      </div>

      {/* Button Examples */}
      <div className="space-y-4">
        <h3 className="text-xl font-semibold text-foreground">Button Variants</h3>
        <div className="flex flex-wrap gap-3">
          <button className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors">
            Primary
          </button>
          <button className="bg-secondary text-secondary-foreground px-4 py-2 rounded-md hover:bg-secondary/80 transition-colors">
            Secondary
          </button>
          <button className="bg-destructive text-destructive-foreground px-4 py-2 rounded-md hover:bg-destructive/90 transition-colors">
            Destructive
          </button>
          <button className="border border-input bg-background text-foreground px-4 py-2 rounded-md hover:bg-accent hover:text-accent-foreground transition-colors">
            Outline
          </button>
          <button className="text-foreground px-4 py-2 rounded-md hover:bg-accent hover:text-accent-foreground transition-colors">
            Ghost
          </button>
        </div>
      </div>

      {/* Text Examples */}
      <div className="space-y-4">
        <h3 className="text-xl font-semibold text-foreground">Text Variants</h3>
        <div className="space-y-2">
          <p className="text-foreground">Default text (text-foreground)</p>
          <p className="text-muted-foreground">Muted text (text-muted-foreground)</p>
          <p className="text-primary">Primary text (text-primary)</p>
          <p className="text-secondary-foreground">Secondary text (text-secondary-foreground)</p>
        </div>
      </div>

      {/* Card Examples */}
      <div className="space-y-4">
        <h3 className="text-xl font-semibold text-foreground">Card Examples</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-card text-card-foreground border border-border rounded-lg p-6">
            <h4 className="font-semibold mb-2">Default Card</h4>
            <p className="text-muted-foreground text-sm">
              This card uses bg-card and text-card-foreground classes.
            </p>
          </div>
          <div className="bg-muted text-muted-foreground border border-border rounded-lg p-6">
            <h4 className="font-semibold mb-2 text-foreground">Muted Card</h4>
            <p className="text-sm">
              This card uses bg-muted with custom text colors.
            </p>
          </div>
        </div>
      </div>

      {/* Form Example */}
      <div className="space-y-4">
        <h3 className="text-xl font-semibold text-foreground">Form Elements</h3>
        <div className="bg-card border border-border rounded-lg p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-foreground mb-1">
              Input Field
            </label>
            <input
              type="text"
              placeholder="Enter some text..."
              className="w-full px-3 py-2 border border-input bg-background text-foreground rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-foreground mb-1">
              Textarea
            </label>
            <textarea
              placeholder="Enter a longer message..."
              rows={3}
              className="w-full px-3 py-2 border border-input bg-background text-foreground rounded-md focus:outline-none focus:ring-2 focus:ring-ring resize-none"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
