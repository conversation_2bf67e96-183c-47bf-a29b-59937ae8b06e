import Image from "next/image";
import { useTranslations } from 'next-intl';
import ThemeShowcase from "./component/ThemeShowcase";
import ExampleCard from "./component/ExampleCard";

export default function Home() {
  const t = useTranslations('home');

  return (
    <div className="min-h-screen bg-background text-foreground">
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-12">
          {/* Hero Section */}
          <div className="text-center space-y-6">
            <div className="space-y-4">
              <h1 className="text-4xl font-bold text-foreground">
                {t('title')}
              </h1>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                {t('subtitle')}
              </p>
            </div>

            <Image
              className="dark:invert mx-auto"
              src="/next.svg"
              alt="Next.js logo"
              width={180}
              height={38}
              priority
            />
          </div>

          {/* Feature Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <ExampleCard
              icon="🌙"
              title={t('features.darkMode.title')}
              description={t('features.darkMode.description')}
            />
            <ExampleCard
              icon="🎨"
              title={t('features.cssVariables.title')}
              description={t('features.cssVariables.description')}
            />
            <ExampleCard
              icon="⚡"
              title={t('features.easyToUse.title')}
              description={t('features.easyToUse.description')}
            />
            <ExampleCard
              icon="🔧"
              title={t('features.reduxIntegration.title')}
              description={t('features.reduxIntegration.description')}
            />
            <ExampleCard
              icon="📱"
              title={t('features.responsive.title')}
              description={t('features.responsive.description')}
            />
            <ExampleCard
              icon="🚀"
              title={t('features.productionReady.title')}
              description={t('features.productionReady.description')}
            />
          </div>

          {/* Theme Showcase */}
          <ThemeShowcase />

          {/* Getting Started */}
          <div className="bg-muted border border-border rounded-lg p-6 text-center">
            <h3 className="text-lg font-semibold text-foreground mb-2">
              {t('gettingStarted.title')}
            </h3>
            <p className="text-muted-foreground mb-4">
              {t('gettingStarted.description', {
                code: 'src/app/page.tsx'
              })}
            </p>
            <div className="flex flex-wrap gap-3 justify-center">
              <button className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors">
                {t('gettingStarted.viewDocs')}
              </button>
              <button className="border border-input bg-background text-foreground px-4 py-2 rounded-md hover:bg-accent hover:text-accent-foreground transition-colors">
                {t('gettingStarted.seeExamples')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
