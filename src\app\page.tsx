import Image from "next/image";
import ThemeShowcase from "./component/ThemeShowcase";
import ExampleCard from "./component/ExampleCard";

export default function Home() {
  return (
    <div className="min-h-screen bg-background text-foreground">
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-12">
          {/* Hero Section */}
          <div className="text-center space-y-6">
            <div className="space-y-4">
              <h1 className="text-4xl font-bold text-foreground">
                🎨 Tailwind CSS Dark Mode
              </h1>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                A complete dark mode setup with class-based theming, custom CSS variables,
                and easy-to-use utility classes. Toggle between themes using the switch in the navigation.
              </p>
            </div>

            <Image
              className="dark:invert mx-auto"
              src="/next.svg"
              alt="Next.js logo"
              width={180}
              height={38}
              priority
            />
          </div>

          {/* Feature Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <ExampleCard
              icon="🌙"
              title="Class-Based Dark Mode"
              description="Simple dark mode implementation using Tailwind's class strategy. Just add 'dark' class to html element."
            />
            <ExampleCard
              icon="🎨"
              title="CSS Variables"
              description="Custom CSS variables for consistent theming. Easy to customize and extend with your brand colors."
            />
            <ExampleCard
              icon="⚡"
              title="Easy to Use"
              description="Semantic class names like bg-background, text-foreground make it intuitive to build themed components."
            />
            <ExampleCard
              icon="🔧"
              title="Redux Integration"
              description="Dark mode state managed with Redux Toolkit for consistent state across your entire application."
            />
            <ExampleCard
              icon="📱"
              title="Responsive Design"
              description="All components work seamlessly across different screen sizes with Tailwind's responsive utilities."
            />
            <ExampleCard
              icon="🚀"
              title="Production Ready"
              description="Optimized setup with proper TypeScript support, ESLint configuration, and Next.js best practices."
            />
          </div>

          {/* Theme Showcase */}
          <ThemeShowcase />

          {/* Getting Started */}
          <div className="bg-muted border border-border rounded-lg p-6 text-center">
            <h3 className="text-lg font-semibold text-foreground mb-2">
              Ready to start building?
            </h3>
            <p className="text-muted-foreground mb-4">
              Edit <code className="bg-accent text-accent-foreground px-2 py-1 rounded font-mono text-sm">src/app/page.tsx</code> to get started
            </p>
            <div className="flex flex-wrap gap-3 justify-center">
              <button className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors">
                View Documentation
              </button>
              <button className="border border-input bg-background text-foreground px-4 py-2 rounded-md hover:bg-accent hover:text-accent-foreground transition-colors">
                See Examples
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
