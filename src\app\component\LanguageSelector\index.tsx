"use client";

import { useLocale, useTranslations } from 'next-intl';
import { useRouter, usePathname } from '@/app/lib/i18n/routing';
import { routing } from '@/app/lib/i18n/routing';
import { useState } from 'react';
import { ChevronDownIcon, LanguageIcon } from '@heroicons/react/24/outline';

const localeNames = {
  en: 'English',
  ar: 'العربية',
  fr: 'Français',
  es: 'Español'
} as const;

const localeFlags = {
  en: '🇺🇸',
  ar: '🇸🇦',
  fr: '🇫🇷',
  es: '🇪🇸'
} as const;

export default function LanguageSelector() {
  const t = useTranslations('language');
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);

  const handleLocaleChange = (newLocale: string) => {
    router.replace(pathname, { locale: newLocale });
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-300 hover:text-white hover:bg-white/5 rounded-md transition-colors"
        aria-label={t('select')}
      >
        <LanguageIcon className="h-4 w-4" />
        <span className="hidden sm:inline">
          {localeFlags[locale as keyof typeof localeFlags]} {localeNames[locale as keyof typeof localeNames]}
        </span>
        <span className="sm:hidden">
          {localeFlags[locale as keyof typeof localeFlags]}
        </span>
        <ChevronDownIcon className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-20">
            <div className="py-1">
              {routing.locales.map((loc) => (
                <button
                  key={loc}
                  onClick={() => handleLocaleChange(loc)}
                  className={`w-full text-left px-4 py-2 text-sm flex items-center gap-3 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                    locale === loc
                      ? 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'
                      : 'text-gray-700 dark:text-gray-300'
                  }`}
                >
                  <span className="text-lg">
                    {localeFlags[loc as keyof typeof localeFlags]}
                  </span>
                  <span>
                    {localeNames[loc as keyof typeof localeNames]}
                  </span>
                  {locale === loc && (
                    <span className="ml-auto text-blue-500">✓</span>
                  )}
                </button>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  );
}
