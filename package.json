{"name": "landpage", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@reduxjs/toolkit": "^2.8.2", "clsx": "^2.1.1", "next": "15.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.4.6", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "^5"}}