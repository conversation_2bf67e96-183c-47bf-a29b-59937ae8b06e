{"common": {"loading": "Loading...", "error": "An error occurred", "save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "confirm": "Confirm", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close"}, "navigation": {"dashboard": "Dashboard", "team": "Team", "projects": "Projects", "calendar": "Calendar", "about": "About", "contact": "Contact", "home": "Home"}, "theme": {"darkMode": "Dark Mode", "lightMode": "Light Mode", "toggleTheme": "Toggle theme", "currentMode": "Currently in {mode} mode"}, "home": {"title": "🎨 Tailwind CSS Dark Mode", "subtitle": "A complete dark mode setup with class-based theming, custom CSS variables, and easy-to-use utility classes. Toggle between themes using the switch in the navigation.", "features": {"darkMode": {"title": "Class-Based Dark Mode", "description": "Simple dark mode implementation using Tailwind's class strategy. Just add 'dark' class to html element."}, "cssVariables": {"title": "CSS Variables", "description": "Custom CSS variables for consistent theming. Easy to customize and extend with your brand colors."}, "easyToUse": {"title": "Easy to Use", "description": "Semantic class names like bg-background, text-foreground make it intuitive to build themed components."}, "reduxIntegration": {"title": "Redux Integration", "description": "Dark mode state managed with Redux Toolkit for consistent state across your entire application."}, "responsive": {"title": "Responsive Design", "description": "All components work seamlessly across different screen sizes with Tailwind's responsive utilities."}, "productionReady": {"title": "Production Ready", "description": "Optimized setup with proper TypeScript support, ESLint configuration, and Next.js best practices."}}, "showcase": {"title": "Theme Showcase", "buttons": {"primary": "Primary", "secondary": "Secondary", "destructive": "Destructive", "outline": "Outline", "ghost": "Ghost"}, "textVariants": "Text Variants", "cardExamples": "Card Examples", "formElements": "Form Elements", "inputPlaceholder": "Enter some text...", "textareaPlaceholder": "Enter a longer message...", "defaultCard": "De<PERSON>ult Card", "defaultCardDescription": "This card uses bg-card and text-card-foreground classes.", "mutedCard": "Muted Card", "mutedCardDescription": "This card uses bg-muted with custom text colors."}, "gettingStarted": {"title": "Ready to start building?", "description": "Edit {code} to get started", "viewDocs": "View Documentation", "seeExamples": "See Examples"}}, "footer": {"copyright": "© 2024 Your App. Built with Next.js and Tailwind CSS."}, "language": {"select": "Select Language", "english": "English", "arabic": "العربية", "french": "Français", "spanish": "Español"}}