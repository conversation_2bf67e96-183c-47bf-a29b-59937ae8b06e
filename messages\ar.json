{"common": {"loading": "جاري التحميل...", "error": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "cancel": "إلغاء", "edit": "تعديل", "delete": "<PERSON><PERSON><PERSON>", "confirm": "تأكيد", "back": "رجوع", "next": "التالي", "previous": "السابق", "close": "إغلاق"}, "navigation": {"dashboard": "لوحة التحكم", "team": "الفريق", "projects": "المشاريع", "calendar": "التقويم", "about": "حو<PERSON>", "contact": "اتصل بنا", "home": "الرئيسية"}, "theme": {"darkMode": "الوضع المظلم", "lightMode": "الوضع المضيء", "toggleTheme": "تبديل المظهر", "currentMode": "الوضع الحالي {mode}"}, "home": {"title": "🎨 الوضع المظلم لـ Tailwind CSS", "subtitle": "إعداد كامل للوضع المظلم مع تصميم قائم على الفئات، متغيرات CSS مخصصة، وفئات أدوات سهلة الاستخدام. بدّل بين المظاهر باستخدام المفتاح في شريط التنقل.", "features": {"darkMode": {"title": "الوضع المظلم القائم على الفئات", "description": "تنفيذ بسيط للوضع المظلم باستخدام استراتيجية فئات Tailwind. فقط أضف فئة 'dark' لعنصر html."}, "cssVariables": {"title": "متغيرات CSS", "description": "متغيرات CSS مخصصة للحصول على تصميم متسق. سهل التخصيص والتوسيع بألوان علامتك التجارية."}, "easyToUse": {"title": "سهل الاستخدام", "description": "أسماء فئات دلالية مثل bg-background، text-foreground تجعل من السهل بناء مكونات ذات مظاهر."}, "reduxIntegration": {"title": "تكامل Redux", "description": "حالة الوضع المظلم مُدارة بـ Redux Toolkit للحصول على حالة متسقة عبر تطبيقك بالكامل."}, "responsive": {"title": "تصميم متجاوب", "description": "جميع المكونات تعمل بسلاسة عبر أحجام الشاشات المختلفة مع أدوات Tailwind المتجاوبة."}, "productionReady": {"title": "جاهز للإنتاج", "description": "إعداد محسّن مع دعم TypeScript المناسب، تكوين ESLint، وأفضل ممارسات Next.js."}}, "showcase": {"title": "عرض المظاهر", "buttons": {"primary": "أساسي", "secondary": "ثانوي", "destructive": "مد<PERSON><PERSON>", "outline": "مخطط", "ghost": "ش<PERSON><PERSON>"}, "textVariants": "أنواع النصوص", "cardExamples": "أمثلة البطاقات", "formElements": "عناصر النموذج", "inputPlaceholder": "أدخل بعض النص...", "textareaPlaceholder": "أدخل رسالة أطول...", "defaultCard": "البطاقة الافتراضية", "defaultCardDescription": "هذه البطاقة تستخدم فئات bg-card و text-card-foreground.", "mutedCard": "البطاقة المكتومة", "mutedCardDescription": "هذه البطاقة تستخدم bg-muted مع ألوان نص مخصصة."}, "gettingStarted": {"title": "مستعد لبدء البناء؟", "description": "عدّل {code} للبدء", "viewDocs": "عرض الوثائق", "seeExamples": "رؤية الأمثلة"}}, "footer": {"copyright": "© 2024 تطبيقك. مبني بـ Next.js و Tailwind CSS."}, "language": {"select": "اختر اللغة", "english": "English", "arabic": "العربية", "french": "Français", "spanish": "Español"}}