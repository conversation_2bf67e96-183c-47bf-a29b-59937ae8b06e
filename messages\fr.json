{"common": {"loading": "Chargement...", "error": "Une erreur s'est produite", "save": "Enregistrer", "cancel": "Annuler", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "Confirmer", "back": "Retour", "next": "Suivant", "previous": "Précédent", "close": "<PERSON><PERSON><PERSON>"}, "navigation": {"dashboard": "Tableau de bord", "team": "Équipe", "projects": "Projets", "calendar": "<PERSON><PERSON><PERSON>", "about": "À propos", "contact": "Contact", "home": "Accueil"}, "theme": {"darkMode": "Mode sombre", "lightMode": "Mode clair", "toggleTheme": "Bas<PERSON><PERSON> le thème", "currentMode": "Actuellement en mode {mode}"}, "home": {"title": "🎨 Mode sombre Tailwind CSS", "subtitle": "Une configuration complète du mode sombre avec des thèmes basés sur les classes, des variables CSS personnalisées et des classes utilitaires faciles à utiliser. Basculez entre les thèmes en utilisant l'interrupteur dans la navigation.", "features": {"darkMode": {"title": "Mode sombre basé sur les classes", "description": "Implémentation simple du mode sombre utilisant la stratégie de classes de Tailwind. Ajoutez simplement la classe 'dark' à l'élément html."}, "cssVariables": {"title": "Variables CSS", "description": "Variables CSS personnalisées pour un thème cohérent. Facile à personnaliser et à étendre avec les couleurs de votre marque."}, "easyToUse": {"title": "Facile à utiliser", "description": "Des noms de classes sémantiques comme bg-background, text-foreground rendent intuitif la construction de composants thématiques."}, "reduxIntegration": {"title": "Intégration Redux", "description": "État du mode sombre géré avec Redux Toolkit pour un état cohérent dans toute votre application."}, "responsive": {"title": "Design réactif", "description": "Tous les composants fonctionnent parfaitement sur différentes tailles d'écran avec les utilitaires réactifs de Tailwind."}, "productionReady": {"title": "Prêt pour la production", "description": "Configuration optimisée avec un support TypeScript approprié, configuration ESLint et meilleures pratiques Next.js."}}, "showcase": {"title": "<PERSON><PERSON><PERSON> des thèmes", "buttons": {"primary": "Primaire", "secondary": "Secondaire", "destructive": "Destructeur", "outline": "<PERSON>tour", "ghost": "<PERSON><PERSON><PERSON>"}, "textVariants": "<PERSON><PERSON><PERSON> de texte", "cardExamples": "Exemples de cartes", "formElements": "Éléments de formulaire", "inputPlaceholder": "Entrez du texte...", "textareaPlaceholder": "Entrez un message plus long...", "defaultCard": "Carte par défaut", "defaultCardDescription": "Cette carte utilise les classes bg-card et text-card-foreground.", "mutedCard": "<PERSON><PERSON> muette", "mutedCardDescription": "Cette carte utilise bg-muted avec des couleurs de texte personnalisées."}, "gettingStarted": {"title": "Prêt à commencer à construire ?", "description": "Modifiez {code} pour commencer", "viewDocs": "Voir la documentation", "seeExamples": "Voir les exemples"}}, "footer": {"copyright": "© 2024 Votre App. Construit avec Next.js et Tailwind CSS."}, "language": {"select": "Sélectionner la langue", "english": "English", "arabic": "العربية", "french": "Français", "spanish": "Español"}}